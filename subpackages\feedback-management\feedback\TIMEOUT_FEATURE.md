# 现场信息反馈页面超时功能

## 功能概述

为现场信息反馈页面添加了2分钟超时功能，确保用户在规定时间内完成信息填写。

## 功能特性

### 1. 倒计时显示
- 在工程名称右侧显示倒计时
- 格式：MM:SS（如 02:00）
- 实时更新，每秒刷新一次

### 2. 警告状态
- 最后30秒时倒计时变为红色
- 添加闪烁动画提醒用户
- 背景色和边框色变为警告色

### 3. 超时处理
- 倒计时结束后自动触发超时
- 显示超时提示弹窗
- 阻止用户继续提交
- 自动返回上一个页面

### 4. 提交检查
- 提交前检查是否已超时
- 超时后点击提交按钮会显示超时提示
- 提交成功后停止倒计时

## 技术实现

### 数据字段
```javascript
timeoutEnabled: false,     // 是否启用超时功能
timeoutDuration: 2 * 60,   // 超时时长（秒）
remainingTime: 0,          // 剩余时间（秒）
timeoutTimer: null,        // 超时定时器
timeoutDisplay: '',        // 显示的倒计时文本
timeoutWarning: false,     // 是否显示警告状态
isTimeout: false,          // 是否已超时
```

### 核心方法
- `startTimeout()`: 启动超时功能
- `updateTimeoutDisplay()`: 更新显示文本
- `handleTimeout()`: 处理超时逻辑
- `returnToPreviousPage()`: 返回上一页

### 生命周期管理
- `onLoad`: 启动超时功能
- `onUnload`: 清理定时器
- 提交成功后停止计时器

## 调试功能

为了方便开发和测试，添加了调试模式：

```
// 在页面URL中添加debug参数
/subpackages/feedback-management/feedback/feedback?taskId=123&debug=true
```

调试模式下超时时间为30秒，便于快速测试功能。

## 样式说明

### 正常状态
- 灰色背景 (#f5f5f5)
- 灰色文字 (#666)
- 灰色边框 (#e0e0e0)

### 警告状态
- 红色背景 (#fff5f5)
- 红色文字 (#ff4444)
- 红色边框 (#ffcccc)
- 闪烁动画效果

## 使用说明

1. 用户进入反馈页面后，倒计时自动开始
2. 倒计时显示在工程名称右侧
3. 最后30秒时显示警告状态
4. 超时后显示提示并返回上一页
5. 提交成功后倒计时停止

## 测试步骤

### 正常功能测试
1. 进入反馈页面，确认倒计时显示为 02:00
2. 等待几秒，确认倒计时正常递减
3. 填写表单并在2分钟内提交，确认提交成功
4. 确认提交成功后倒计时消失

### 超时功能测试
1. 使用调试模式：在URL中添加 `&debug=true`
2. 进入页面后倒计时显示为 00:30
3. 等待30秒不操作
4. 确认超时后显示提示弹窗
5. 点击确定后返回上一页

### 警告状态测试
1. 使用调试模式进入页面
2. 等待倒计时到最后30秒（或正常模式下最后30秒）
3. 确认倒计时变为红色并有闪烁效果

### 页面切换测试
1. 进入反馈页面
2. 切换到其他应用或页面
3. 返回反馈页面
4. 确认倒计时继续正常工作

## 注意事项

1. 定时器会在页面卸载时自动清理
2. 超时后无法继续提交表单
3. 提交成功后会立即停止倒计时
4. 调试模式仅用于开发测试
5. 页面隐藏时会暂停计时，显示时会恢复
6. 倒计时在小屏幕设备上会自动换行显示
